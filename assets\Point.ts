import { _decorator, Component, math, Node, tween } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('Point')
export class Point extends Component {


    @property
    initialAngle: number = -70; // 初始摆动角度（左边界）
    @property
    maxAngle: number = 10; // 最大摆动角度（右边界）
    @property
    centerAngle: number = -30; // 居中角度
    @property
    swingDuration: number = 0.8; // 初始摆动持续时间
    @property
    decelerationRate: number = 0.9; // 摆动幅度减速系数 (0-1)
    @property
    speedUpRate: number = 1; // 摆动速度加快系数 (0-1)
    @property
    minAngleThreshold: number = 2; // 停止角度阈值

    private currentTween: any = null;
    private currentAmplitude: number = 0; // 当前摆动幅度
    private currentDuration: number = 0; // 当前摆动持续时间
    private isSwinging: boolean = false;
    private swingDirection: number = 1; // 摆动方向：1为向右，-1为向左

    @property(Node)
    private _point: Node = null;

    start() {

        this.startSwing();
    }



    startSwing() {
        if (this.isSwinging) return;

        this.isSwinging = true;

        // 计算初始摆动幅度（从中心到边界的距离）
        const leftAmplitude = Math.abs(this.centerAngle - this.initialAngle); // 从中心到左边界
        const rightAmplitude = Math.abs(this.maxAngle - this.centerAngle); // 从中心到右边界
        this.currentAmplitude = Math.max(leftAmplitude, rightAmplitude); // 使用较大的幅度

        this.currentDuration = this.swingDuration;
        this.swingDirection = 1; // 开始向右摆动

        // 设置初始位置到左边界
        this.node.angle = this.initialAngle;

        // 开始向右摆动到右边界
        this.swingTo(this.maxAngle);
    }

    swingTo(targetAngle: number) {
        this.currentTween = tween(this.node)
            .to(this.currentDuration, { angle: targetAngle })
            .call(() => {
                // 应用惯性效果：减小摆动幅度，加快摆动速度
                this.currentAmplitude *= this.decelerationRate;
                this.currentDuration *= this.speedUpRate;

                // 检查是否停止摆动
                if (this.currentAmplitude < this.minAngleThreshold) {
                    this.stopSwing();
                } else {
                    // 切换摆动方向
                    this.swingDirection *= -1;

                    // 计算下一个目标角度
                    const nextTarget = this.centerAngle + (this.currentAmplitude * this.swingDirection);

                    // 确保角度在范围内
                    const clampedTarget = math.clamp(nextTarget, this.initialAngle, this.maxAngle);

                    this.swingTo(clampedTarget);
                }
            })
            .start();
    }

    stopSwing() {
        if (this.currentTween) {
            this.currentTween.stop();
            this.currentTween = null;
        }
        this.isSwinging = false;

        // 最终归位到居中位置
        tween(this.node)
            .to(0.3, { angle: this.centerAngle })
            .start();
    }

    // 外部调用添加额外惯性
    addInertia(extraForce: number) {
        if (!this.isSwinging) {
            this.startSwing();
            return;
        }

        // 增加摆动幅度
        this.currentAmplitude += extraForce;

        // 限制最大摆动幅度
        const maxAmplitude = Math.max(
            Math.abs(this.centerAngle - this.initialAngle),
            Math.abs(this.maxAngle - this.centerAngle)
        ) * 1.5;
        this.currentAmplitude = math.clamp(this.currentAmplitude, 0, maxAmplitude);

        // 重新计算摆动
        if (this.currentTween) {
            this.currentTween.stop();
        }

        // 根据当前方向计算下一个目标
        const nextTarget = this.centerAngle + (this.currentAmplitude * this.swingDirection);
        const clampedTarget = math.clamp(nextTarget, this.initialAngle, this.maxAngle);
        this.swingTo(clampedTarget);
    }

    // update(deltaTime: number) {
    //     // 如果需要在每帧更新逻辑，可以在这里添加
    // }
}


