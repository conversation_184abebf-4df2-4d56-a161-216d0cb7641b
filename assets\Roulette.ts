import { _decorator, Component, easing, Node, tween, Tween } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('Roulette')
export class Roulette extends Component {

    //每个数字的角度
    private numberAngles = [-313, -89, -255, -332, -274, -128, -216, -11, -157, -50, -137, -177, -352, -196, -69, -293, -109, -235, -31, -284, -79, -264, -40, -147, -118, -245, -323, -206, -1, -21, -167, -59, -303, -99, -226, -342, -187];

    start() {


        const currentNumber = 5;
        let fullRotation = 360 * 6;

        const numberAngle = this.numberAngles[currentNumber];
        //6圈 + 需要转动到的数字的角度 
        let finalAngle = -(fullRotation + Math.abs(numberAngle));
        
        const objs = {
            angle: 0,
            number: 0
        }

        const t1 = tween(objs)
            .to(15, { angle: finalAngle }, {
                easing:  'easeInOutBack',
                progress: (start, end, current, ratio) => {
                    let angle = start + (end - start) * ratio;
                    this.node.angle = angle % 360;
                    return ratio;
                }
            });

        // const t2 = tween(this.hideRouletteNode)
        //     .delay(14.8)
        //     .by(3, { angle: -3 }, {
        //         easing: easing.linear,
        //     })
        //     .by(3, { angle: 5 }, {
        //         easing: easing.linear,
        //     })
        //     .call(() => {
        //         this.renderWinner();
        //         this.zoomOut();
        //     })
        //     .by(4, { angle: -2 }, {
        //         easing: easing.linear
        //     });

        tween(objs).parallel(t1).start();
    }

    update(deltaTime: number) {

    }
}


